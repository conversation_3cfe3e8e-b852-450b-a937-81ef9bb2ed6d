import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, CircularProgress } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';

const LandingPage = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading, user, error } = useAuth();

  useEffect(() => {
    console.log('🔐 LandingPage: Current state:', {
      isLoading,
      isAuthenticated,
      hasUser: !!user,
      error,
      currentPath: window.location.pathname
    });

    console.log('🔐 Environment variables:', {
      VITE_APP_BASE_NAME: import.meta.env.VITE_APP_BASE_NAME,
      VITE_KEYCLOAK_URL: import.meta.env.VITE_KEYCLOAK_URL,
      VITE_KEYCLOAK_REALM: import.meta.env.VITE_KEYCLOAK_REALM,
      VITE_API_URL: import.meta.env.VITE_API_URL,
      // Legacy variables
      REACT_APP_KEYCLOAK_URL: import.meta.env.REACT_APP_KEYCLOAK_URL,
      REACT_APP_KEYCLOAK_REALM: import.meta.env.REACT_APP_KEYCLOAK_REALM,
      REACT_APP_API_URL: import.meta.env.REACT_APP_API_URL
    });

    // Add a small delay to ensure auth context is properly initialized
    const timer = setTimeout(() => {
      // If user is authenticated, redirect to dashboard
      if (!isLoading && isAuthenticated) {
        console.log('🔐 LandingPage: User authenticated, redirecting to dashboard');
        navigate('/app/dashboard/default', { replace: true });
      } else if (!isLoading && !isAuthenticated) {
        // If user is not authenticated, redirect to login
        console.log('🔐 LandingPage: User not authenticated, redirecting to login');
        navigate('/pages/login', { replace: true });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading, navigate, user, error]);

  return (
    <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100vh" gap={2}>
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        Loading Jihene-Line Backoffice...
      </Typography>
      <Typography variant="body2" color="textSecondary">
        Please wait while we prepare your workspace
      </Typography>
    </Box>
  );
};

export default LandingPage;
