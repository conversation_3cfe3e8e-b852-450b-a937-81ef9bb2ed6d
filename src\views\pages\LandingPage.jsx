import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, CircularProgress } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';

const LandingPage = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading, user, error } = useAuth();

  useEffect(() => {
    console.log('🔐 LandingPage: Current state:', {
      isLoading,
      isAuthenticated,
      hasUser: !!user,
      error,
      currentPath: window.location.pathname
    });

    console.log('🔐 Environment variables:', {
      VITE_APP_BASE_NAME: import.meta.env.VITE_APP_BASE_NAME,
      VITE_KEYCLOAK_URL: import.meta.env.VITE_KEYCLOAK_URL,
      VITE_KEYCLOAK_REALM: import.meta.env.VITE_KEYCLOAK_REALM,
      VITE_API_URL: import.meta.env.VITE_API_URL,
      // Legacy variables
      REACT_APP_KEYCLOAK_URL: import.meta.env.REACT_APP_KEYCLOAK_URL,
      REACT_APP_KEYCLOAK_REALM: import.meta.env.REACT_APP_KEYCLOAK_REALM,
      REACT_APP_API_URL: import.meta.env.REACT_APP_API_URL
    });

    // Temporarily disable auto-redirect for testing
    // Add a small delay to ensure auth context is properly initialized
    const timer = setTimeout(() => {
      // If user is authenticated, redirect to dashboard
      if (!isLoading && isAuthenticated) {
        console.log('🔐 LandingPage: User authenticated, redirecting to dashboard');
        navigate('/app/dashboard/default', { replace: true });
      } else if (!isLoading && !isAuthenticated) {
        // If user is not authenticated, redirect to login
        console.log('🔐 LandingPage: User not authenticated, redirecting to login');
        // Temporarily comment out auto-redirect for testing
        // navigate('/pages/login', { replace: true });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading, navigate, user, error]);

  return (
    <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100vh" gap={2} p={3}>
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        Loading Jihene-Line Backoffice...
      </Typography>
      <Typography variant="body2" color="textSecondary">
        Please wait while we prepare your workspace
      </Typography>

      {/* Debug Information */}
      <Box mt={4} p={2} border="1px solid #ccc" borderRadius={2} maxWidth={600}>
        <Typography variant="h6" gutterBottom>
          Debug Information
        </Typography>
        <Typography variant="body2">Loading: {isLoading ? 'Yes' : 'No'}</Typography>
        <Typography variant="body2">Authenticated: {isAuthenticated ? 'Yes' : 'No'}</Typography>
        <Typography variant="body2">Has User: {user ? 'Yes' : 'No'}</Typography>
        <Typography variant="body2">Error: {error || 'None'}</Typography>
        <Typography variant="body2">Current Path: {window.location.pathname}</Typography>
        <Typography variant="body2">Base Name: {import.meta.env.VITE_APP_BASE_NAME || 'undefined'}</Typography>
      </Box>

      {/* Test Links */}
      <Box mt={2} display="flex" gap={2} flexWrap="wrap">
        <a href="/test" style={{ color: '#1976d2', textDecoration: 'none' }}>
          Test Route
        </a>
        <a href="/pages/login" style={{ color: '#1976d2', textDecoration: 'none' }}>
          Login
        </a>
        <a href="/app/dashboard/default" style={{ color: '#1976d2', textDecoration: 'none' }}>
          Dashboard
        </a>
        <a href="/keycloak-test" style={{ color: '#1976d2', textDecoration: 'none' }}>
          Keycloak Test
        </a>
      </Box>
    </Box>
  );
};

export default LandingPage;
