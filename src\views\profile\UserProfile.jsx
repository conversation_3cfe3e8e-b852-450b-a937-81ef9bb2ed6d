import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Grid,
  Divider,
  Button,
  Alert,
  CircularProgress,
  Stack,
  Paper
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { IconRefresh, IconUser, IconMail, IconKey, IconShield, IconLogout } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import { useAuth } from 'contexts/AuthContext';
import authService from 'services/authService';

// assets
import User1 from 'assets/images/users/user-round.svg';

// ==============================|| USER PROFILE PAGE ||============================== //

export default function UserProfile() {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, refreshUser, logout } = useAuth();
  const [loading, setLoading] = useState(false);
  const [loggingOut, setLoggingOut] = useState(false);
  const [error, setError] = useState('');
  const [lastUpdated, setLastUpdated] = useState(null);

  // Refresh user data from API
  const handleRefreshUserData = async () => {
    try {
      setLoading(true);
      setError('');
      console.log('🔄 Refreshing user data from /api/auth/user...');

      await refreshUser();
      setLastUpdated(new Date());
      console.log('✅ User data refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh user data:', error);
      setError('Failed to refresh user data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      console.log('🔐 Profile page logout initiated');
      setLoggingOut(true);
      // The logout function will handle the Keycloak redirect
      await logout();

      // Note: We won't reach this point because logout() redirects to Keycloak
      console.log('🔐 Profile page logout completed');
    } catch (error) {
      console.error('❌ Profile page logout failed:', error);
      // If logout fails, still try to redirect to login
      navigate('/pages/login');
    } finally {
      setLoggingOut(false);
    }
  };

  // Auto-refresh user data on component mount
  useEffect(() => {
    console.log('🔍 Profile page mounted, current user data:', user);
    handleRefreshUserData();
  }, []);

  // Debug user data changes
  useEffect(() => {
    console.log('🔍 User data changed in profile:', user);
  }, [user]);

  if (!user) {
    return (
      <MainCard title="User Profile">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <CircularProgress />
        </Box>
      </MainCard>
    );
  }

  return (
    <MainCard
      title="User Profile"
      secondary={
        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            size="small"
            startIcon={loading ? <CircularProgress size={16} /> : <IconRefresh />}
            onClick={handleRefreshUserData}
            disabled={loading || loggingOut}
          >
            {loading ? 'Refreshing...' : 'Refresh Data'}
          </Button>
          <Button
            variant="outlined"
            size="small"
            color="error"
            startIcon={loggingOut ? <CircularProgress size={16} /> : <IconLogout />}
            onClick={handleLogout}
            disabled={loading || loggingOut}
          >
            {loggingOut ? 'Logging out...' : 'Logout'}
          </Button>
        </Stack>
      }
    >
      <Grid container spacing={3}>
        {/* Error Display */}
        {error && (
          <Grid item xs={12}>
            <Alert severity="error" onClose={() => setError('')}>
              {error}
            </Alert>
          </Grid>
        )}

        {/* Last Updated Info */}
        {lastUpdated && (
          <Grid item xs={12}>
            <Alert severity="info">Data last updated: {lastUpdated.toLocaleString()}</Alert>
          </Grid>
        )}

        {/* Profile Header */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
            <Stack direction="row" spacing={3} alignItems="center">
              <Avatar
                src={User1}
                alt={user.name}
                sx={{
                  width: 80,
                  height: 80,
                  border: `3px solid ${theme.palette.primary.main}`
                }}
              />
              <Box flex={1}>
                <Typography variant="h3" gutterBottom>
                  {user.name || 'Unknown User'}
                </Typography>
                <Typography variant="body1" color="textSecondary" gutterBottom>
                  {user.email || 'No email provided'}
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  {user.roles?.map((role, index) => (
                    <Chip
                      key={index}
                      label={role}
                      size="small"
                      color={role === 'admin' ? 'error' : role === 'partenaire' ? 'warning' : 'primary'}
                      icon={<IconShield size={16} />}
                    />
                  )) || <Chip label="No roles assigned" size="small" color="default" />}
                </Box>
              </Box>
            </Stack>
          </Paper>
        </Grid>

        {/* User Details */}
        <Grid item xs={12} md={6}>
          <Card elevation={1}>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconUser size={20} />
                Personal Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Stack spacing={2}>
                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Full Name
                  </Typography>
                  <Typography variant="body1">{user.name || 'Not provided'}</Typography>
                </Box>

                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Email Address
                  </Typography>
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <IconMail size={16} />
                    {user.email || 'Not provided'}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    User ID
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                    {user.id || 'Not available'}
                  </Typography>
                </Box>

                {user.keycloak_id && (
                  <Box>
                    <Typography variant="subtitle2" color="textSecondary">
                      Keycloak ID
                    </Typography>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                      {user.keycloak_id}
                    </Typography>
                  </Box>
                )}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Access & Permissions */}
        <Grid item xs={12} md={6}>
          <Card elevation={1}>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconKey size={20} />
                Access & Permissions
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Stack spacing={2}>
                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Account Type
                  </Typography>
                  <Typography variant="body1">Administrator Account</Typography>
                </Box>

                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Assigned Roles
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    {user.roles?.map((role, index) => (
                      <Chip
                        key={index}
                        label={role}
                        size="small"
                        variant="filled"
                        color={role === 'admin' ? 'error' : role === 'partenaire' ? 'warning' : 'primary'}
                      />
                    )) || (
                      <Typography variant="body2" color="textSecondary">
                        No roles assigned
                      </Typography>
                    )}
                  </Box>
                </Box>

                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Authentication Method
                  </Typography>
                  <Typography variant="body1">Keycloak SSO</Typography>
                </Box>

                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Access Level
                  </Typography>
                  <Typography variant="body1" color="error.main" sx={{ fontWeight: 'bold' }}>
                    Full Administrative Access
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Debug Info */}
        <Grid item xs={12}>
          <Card elevation={1} sx={{ backgroundColor: '#f5f5f5' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Debug Information
              </Typography>
              <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                {JSON.stringify(user, null, 2)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Data Source Info */}
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Data Source:</strong> This information is fetched in real-time from the backend API endpoint
              <code style={{ margin: '0 4px', padding: '2px 4px', backgroundColor: 'rgba(0,0,0,0.1)', borderRadius: '3px' }}>
                /api/auth/user
              </code>
              and synchronized with Keycloak authentication.
            </Typography>
          </Alert>
        </Grid>
      </Grid>
    </MainCard>
  );
}
